using FluentAssertions;
using GCP.Common;
using GCP.Eventbus.Infrastructure;
using GCP.FunctionPool.Flow.Services;
using GCP.Tests.Base;
using Microsoft.Extensions.DependencyInjection;
using Xunit;
using Xunit.Abstractions;

namespace GCP.Tests.Workflow
{
    /// <summary>
    /// 消息消费者错误处理测试
    /// </summary>
    public class MessageConsumerErrorHandlingTests : BaseTest
    {
        public MessageConsumerErrorHandlingTests(ITestOutputHelper output) : base(output)
        {
        }

        [Fact]
        public async Task DataCache_WithNullProjectId_ShouldUseDefaultValue()
        {
            // Arrange
            await InitializeTestDataAsync();
            var dataCache = GetService<DataCache>();
            
            // 创建一个没有ProjectId的上下文
            var context = new FunctionContext();
            context.Args = new Dictionary<string, object>();
            // 不设置任何ProjectId相关的值
            
            dataCache.SetTestContext(context, "test-solution-001", null); // ProjectId设为null

            var writeData = new DataCacheWrite
            {
                Name = "测试空ProjectId",
                Description = "测试ProjectId为空时的处理",
                CacheKey = new DataValue { Type = "text", TextValue = "test_key_null_project" },
                CacheValue = new DataValue { Type = "text", TextValue = "test_value" }
            };

            // Act & Assert - 应该不抛出异常，而是使用默认值
            var exception = await Record.ExceptionAsync(() => dataCache.WriteCache(writeData));
            exception.Should().BeNull("应该能够处理ProjectId为空的情况");

            Output.WriteLine("成功处理ProjectId为空的情况");
        }

        [Fact]
        public async Task DataCache_WithEmptyProjectId_ShouldUseDefaultValue()
        {
            // Arrange
            await InitializeTestDataAsync();
            var dataCache = GetService<DataCache>();
            
            // 创建一个ProjectId为空字符串的上下文
            var context = new FunctionContext();
            context.Args = new Dictionary<string, object>();
            context.globalData["ProjectId"] = ""; // 设置为空字符串
            
            dataCache.SetTestContext(context, "test-solution-001", "");

            var writeData = new DataCacheWrite
            {
                Name = "测试空ProjectId字符串",
                Description = "测试ProjectId为空字符串时的处理",
                CacheKey = new DataValue { Type = "text", TextValue = "test_key_empty_project" },
                CacheValue = new DataValue { Type = "text", TextValue = "test_value" }
            };

            // Act & Assert - 应该不抛出异常，而是使用默认值
            var exception = await Record.ExceptionAsync(() => dataCache.WriteCache(writeData));
            exception.Should().BeNull("应该能够处理ProjectId为空字符串的情况");

            Output.WriteLine("成功处理ProjectId为空字符串的情况");
        }

        [Fact]
        public async Task DataCache_WithWhitespaceProjectId_ShouldUseDefaultValue()
        {
            // Arrange
            await InitializeTestDataAsync();
            var dataCache = GetService<DataCache>();
            
            // 创建一个ProjectId为空白字符的上下文
            var context = new FunctionContext();
            context.Args = new Dictionary<string, object>();
            context.globalData["ProjectId"] = "   "; // 设置为空白字符
            
            dataCache.SetTestContext(context, "test-solution-001", "   ");

            var writeData = new DataCacheWrite
            {
                Name = "测试空白ProjectId",
                Description = "测试ProjectId为空白字符时的处理",
                CacheKey = new DataValue { Type = "text", TextValue = "test_key_whitespace_project" },
                CacheValue = new DataValue { Type = "text", TextValue = "test_value" }
            };

            // Act & Assert - 应该不抛出异常，而是使用默认值
            var exception = await Record.ExceptionAsync(() => dataCache.WriteCache(writeData));
            exception.Should().BeNull("应该能够处理ProjectId为空白字符的情况");

            Output.WriteLine("成功处理ProjectId为空白字符的情况");
        }

        [Fact]
        public void ResiliencePipelineManager_WithNullKey_ShouldThrowArgumentException()
        {
            // Act & Assert
            var exception = Assert.Throws<ArgumentException>(() => 
                ResiliencePipelineManager.TryAdd(null, () => null));
            
            exception.Message.Should().Contain("Pipeline key 不能为空");
            Output.WriteLine("正确处理了null key的情况");
        }

        [Fact]
        public void ResiliencePipelineManager_WithEmptyKey_ShouldThrowArgumentException()
        {
            // Act & Assert
            var exception = Assert.Throws<ArgumentException>(() => 
                ResiliencePipelineManager.TryAdd("", () => null));
            
            exception.Message.Should().Contain("Pipeline key 不能为空");
            Output.WriteLine("正确处理了空字符串key的情况");
        }

        [Fact]
        public void ResiliencePipelineManager_TryRemoveWithNullKey_ShouldReturnFalse()
        {
            // Act
            var result = ResiliencePipelineManager.TryRemove(null);
            
            // Assert
            result.Should().BeFalse("对于null key应该返回false而不抛出异常");
            Output.WriteLine("正确处理了TryRemove null key的情况");
        }

        [Fact]
        public async Task ResiliencePipelineManager_ExecuteAsyncWithNullKey_ShouldThrowArgumentException()
        {
            // Act & Assert
            var exception = await Assert.ThrowsAsync<ArgumentException>(() => 
                ResiliencePipelineManager.ExecuteAsync(null, (token) => Task.CompletedTask));
            
            exception.Message.Should().Contain("Pipeline key 不能为空");
            Output.WriteLine("正确处理了ExecuteAsync null key的情况");
        }

        [Fact]
        public async Task DataCache_ReadWithNullCacheKey_ShouldThrowCustomException()
        {
            // Arrange
            await InitializeTestDataAsync();
            var dataCache = GetService<DataCache>();
            SetTestContext(dataCache);

            var readData = new DataCacheRead
            {
                Name = "测试空缓存键",
                Description = "测试缓存键为空时的处理",
                CacheKey = new DataValue { Type = "text", TextValue = null },
                DefaultValue = new DataValue { Type = "text", TextValue = "default" }
            };

            // Act & Assert
            var exception = await Assert.ThrowsAsync<CustomException>(() => dataCache.ReadCache(readData));
            exception.Message.Should().Contain("缓存键不能为空");
            Output.WriteLine("正确处理了空缓存键的情况");
        }
    }
}
